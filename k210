# 精简版K210矩形自动追踪系统
# 专注于矩形检测和中心追踪功能

from machine import UART
import time
import sensor, image, lcd
from fpioa_manager import fm
import gc

# 硬件初始化
def hardware_init():
    """K210硬件初始化"""
    # 摄像头初始化
    sensor.reset()
    sensor.set_pixformat(sensor.GRAYSCALE)
    sensor.set_framesize(sensor.QQVGA)
    sensor.set_brightness(-2)
    sensor.set_saturation(-2)
    sensor.skip_frames(time=2000)
    
    # LCD初始化
    lcd.init()
    lcd.clear(lcd.WHITE)
    
    print("K210硬件初始化完成")

# 舵机控制函数
def func_servo(id0, posit0, interval0):
    """舵机控制函数"""
    ZT1 = 0xFF
    ZT2 = 0xFF
    DATA1 = 0X2A
    DATA2 = (posit0 >> 8) & 0xff
    DATA3 = posit0 & 0xff
    DATA4 = (interval0 >> 8) & 0xff
    DATA5 = interval0 & 0xff
    data_length = 0x07
    WriteDATA = 0X03
    GetChecksum = (~(id0 + data_length + WriteDATA + DATA1 + DATA2 + DATA3 + DATA4 + DATA5)) & 0xff
    text = bytes([ZT1, ZT2, id0, data_length, WriteDATA, DATA1, DATA2, DATA3, DATA4, DATA5, GetChecksum])
    uart.write(text)

def main():
    """主程序 - 纯矩形追踪功能"""
    global uart
    
    # 硬件初始化
    hardware_init()
    
    # 追踪参数初始化 - 完全按照原代码
    clock = time.clock()
    target_x = 70  # 对应原代码 xin = 70
    target_y = 70  # 对应原代码 yin = 70
    
    # PID控制参数 - 完全保持原代码逻辑
    # 原代码：P = (ZX - xin) * 0, I = (ZX - xin) * 1 + I
    kp_x = 0       # X轴比例系数（原代码为0）
    ki_x = 1       # X轴积分系数（原代码为1）
    kp_y = 0       # Y轴比例系数（原代码为0）
    ki_y = 1       # Y轴积分系数（原代码为1）

    # PID积分项（对应原代码的I和I2）
    integral_x = 0  # 对应原代码的I
    integral_y = 0  # 对应原代码的I2
    
    # 舵机参数
    servo_x_center = 2048  # X轴舵机中心位置
    servo_y_center = 2048  # Y轴舵机中心位置
    servo_x_pos = servo_x_center
    servo_y_pos = servo_y_center
    interval = 100  # 舵机运动时间
    ID1 = 0x01     # X轴舵机ID
    ID2 = 0x02     # Y轴舵机ID
    
    # 串口初始化 - 仅用于舵机控制
    fm.register(0, fm.fpioa.UART1_RX, force=True)
    fm.register(1, fm.fpioa.UART1_TX, force=True)
    uart = UART(UART.UART1, 115200, read_buf_len=4096)
    
    # 舵机回中位
    func_servo(ID1, servo_x_center, 1000)
    func_servo(ID2, servo_y_center, 1000)
    time.sleep(1)
    
    print("矩形追踪系统启动...")
    
    # 主循环 - 纯追踪功能
    while True:
        gc.collect()
        clock.tick()
        
        # 图像采集
        img = sensor.snapshot()
        
        # 图像预处理 - 边缘增强
        img.laplacian(1, sharpen=True)
        
        # 矩形检测 - 完全按照原代码逻辑处理所有矩形
        rectangles = img.find_rects(threshold=30000)

        # 处理所有检测到的矩形（对应原代码的for循环）
        for r in rectangles:
            # 绘制检测结果
            img.draw_rectangle(r.rect(), color=(255, 0, 0))
            for p in r.corners():
                img.draw_circle(p[0], p[1], 5, color=(0, 255, 0))

            # 计算矩形中心 - 完全对应原代码
            corners = r.corners()
            center_x = (corners[0][0] + corners[1][0] + corners[2][0] + corners[3][0]) / 4
            center_y = (corners[0][1] + corners[1][1] + corners[2][1] + corners[3][1]) / 4

            # 绘制中心点
            img.draw_circle(int(center_x), int(center_y), 8, color=(0, 0, 255))
            
            # PID控制计算 - 完全按照原代码逻辑
            error_x = center_x - target_x  # 对应原代码 (ZX - xin)
            error_y = center_y - target_y  # 对应原代码 (ZY - yin)

            # 积分项更新 - 对应原代码 I = (ZX - xin) * 1 + I
            integral_x = error_x * ki_x + integral_x  # 完全对应原代码
            integral_y = error_y * ki_y + integral_y  # 完全对应原代码

            # PID输出 - 对应原代码 PI = P + I
            P_x = error_x * kp_x  # 对应原代码 P = (ZX - xin) * 0
            P_y = error_y * kp_y  # 对应原代码 P2 = (ZY - yin) * 0
            output_x = P_x + integral_x  # 对应原代码 PI = P + I
            output_y = P_y + integral_y  # 对应原代码 PI2 = P2 + I2
            
            # 舵机位置计算 - 对应原代码逻辑
            servo_x_pos = servo_x_center - int(output_x)  # 对应原代码 positx - PI
            servo_y_pos = servo_y_center + int(output_y)  # 对应原代码 posit + PI2
            
            # 舵机位置限制
            servo_x_pos = max(1000, min(3000, servo_x_pos))
            servo_y_pos = max(1000, min(3000, servo_y_pos))
            
            # 舵机控制
            func_servo(ID1, servo_x_pos, interval)
            func_servo(ID2, servo_y_pos, interval)
            
            # 显示追踪信息
            img.draw_string(0, 0, "Tracking: ON", color=(0, 255, 0), scale=2)
            img.draw_string(0, 20, "X:%d Y:%d" % (int(center_x), int(center_y)), color=(255, 255, 255), scale=1)

        # 注意：原代码没有else分支，无矩形时继续使用上次的中心值
        
        # 绘制屏幕中心十字线
        img.draw_cross(target_x, target_y, color=(255, 255, 0), scale=4)
        
        # 显示FPS
        fps = clock.fps()
        img.draw_string(0, 100, "FPS: %2.1f" % fps, color=(255, 0, 0), scale=1)
        
        # LCD显示
        lcd.display(img)

# 程序入口
if __name__ == "__main__":
    main()
